const API = require("@/config/api")
const UTIL = require("@/utils/util")

/**
 * 我的首页数据服务
 * 提供统一的数据加载、缓存管理功能
 */

/**
 * 构建请求参数
 * @param {number} activeTabIndex - 主tab索引
 * @param {number} subActiveTabIndex - 子tab索引
 * @param {number} page - 页码
 * @param {number} size - 每页数量
 */
function buildRequestParams(activeTabIndex, subActiveTabIndex, page, size) {
  // 确定API URL
  const url = activeTabIndex === 2 ? API.getFootPrintList : API.getFollowsList
  
  // 确定item_type
  let item_type
  if (activeTabIndex === 0) {
    item_type = "article"
  } else if (activeTabIndex === 1) {
    item_type = "job"
  } else {
    // activeTabIndex === 2 (浏览足迹)
    item_type = subActiveTabIndex === 0 ? "article" : "job"
  }
  
  return {
    url,
    params: {
      item_type,
      page,
      size,
    }
  }
}

/**
 * 处理列表响应数据
 * @param {Object} res - 响应数据
 * @param {number} activeTabIndex - 主tab索引
 * @param {number} size - 每页数量
 */
function processListResponse(res, activeTabIndex, size) {
  const resData = res?.data
  let newList = []
  let tips = ""
  
  // 根据不同的tab处理数据结构
  if (activeTabIndex === 2) {
    // 浏览足迹返回的是 { list: [], tips: "" }
    newList = resData?.list || []
    tips = resData?.tips || ""
  } else {
    // 关注列表直接返回数组
    newList = resData || []
  }
  
  // 判断是否还有更多数据
  const hasMore = newList.length >= size
  
  return {
    list: newList,
    tips,
    hasMore,
  }
}

/**
 * 合并列表数据
 * @param {Array} existingList - 现有数据
 * @param {Array} newList - 新数据
 * @param {boolean} isLoadMore - 是否为加载更多
 */
function mergeListData(existingList, newList, isLoadMore) {
  if (isLoadMore) {
    // 分页加载：追加到现有数据
    return [...existingList, ...newList]
  } else {
    // 首次加载或刷新：直接使用新数据
    return newList
  }
}

/**
 * 更新缓存数据
 * @param {Object} tabDataCache - 当前缓存对象
 * @param {string} tabKey - tab标识
 * @param {Array} updatedList - 更新后的列表
 * @param {boolean} hasMore - 是否还有更多数据
 * @param {boolean} isLoadMore - 是否为加载更多
 * @param {number} currentPage - 当前页码
 */
function updateTabCache(tabDataCache, tabKey, updatedList, hasMore, isLoadMore, currentPage) {
  return {
    ...tabDataCache,
    [tabKey]: {
      list: updatedList,
      hasMore,
      page: isLoadMore ? currentPage + 1 : 2, // 下次请求的页码
    }
  }
}

/**
 * 生成tab标识
 * @param {number} activeTabIndex - 主tab索引
 * @param {number} subActiveTabIndex - 子tab索引
 */
function generateTabKey(activeTabIndex, subActiveTabIndex) {
  if (activeTabIndex === 2) {
    return `${activeTabIndex}-${subActiveTabIndex}`
  }
  return activeTabIndex.toString()
}

/**
 * 初始化缓存数据结构
 */
function initTabDataCache() {
  return {
    0: { list: [], hasMore: true, page: 1 }, // 关注公告
    1: { list: [], hasMore: true, page: 1 }, // 关注职位
    "2-0": { list: [], hasMore: true, page: 1 }, // 浏览足迹-公告
    "2-1": { list: [], hasMore: true, page: 1 }, // 浏览足迹-职位
  }
}

/**
 * 清除指定tab的缓存
 * @param {Object} tabDataCache - 当前缓存对象
 * @param {string} tabKey - tab标识
 */
function clearTabCache(tabDataCache, tabKey) {
  return {
    ...tabDataCache,
    [tabKey]: {
      list: [],
      hasMore: true,
      page: 1,
    }
  }
}

module.exports = {
  buildRequestParams,
  processListResponse,
  mergeListData,
  updateTabCache,
  generateTabKey,
  initTabDataCache,
  clearTabCache,
}
