const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const MyHomeDataService = require("@/services/myHomeDataService")
const APP = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    navigationBarHeight: "",
    tabHeight: "",
    isLogin: true,
    resumeProgress: 23, // 简历完善度
    activeTabIndex: 1,
    subActiveTabIndex: 0,
    tipsShow: true,
    isSticky: false,
    baseInfo: null,
    tabList: [
      {
        title: "关注公告",
        subList: [],
      },
      {
        title: "关注职位",
        subList: [],
      },
      {
        title: "浏览足迹",
        subList: [
          {
            title: "公告",
          },
          {
            title: "职位",
          },
        ],
      },
    ],
    dataList: [],
    // 弹窗相关
    showOptionsPopup: false, // 显示操作选项弹窗
    showTagPopup: false, // 显示添加标签弹窗
    currentJobInfo: null, // 当前操作的职位信息
    // 一级弹窗选项数据
    optionsData: [],
    // 二级弹窗选项数据
    tagOptionsData: [],
    // 分页相关状态
    page: 1,
    size: 20,
    hasMore: true, // 是否还有更多数据
    isLoading: false, // 是否正在加载
    tips: "",
    pkListIds: [],
    // 数据缓存 - 为每个tab缓存数据，避免重复请求
    tabDataCache: MyHomeDataService.initTabDataCache(),
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    await APP.checkLoadRequest()
    // 初始化tab滚动位置记录对象
    this.tabScrollPositions = {
      0: 0, // 关注公告
      1: 0, // 关注职位
      "2-0": 0, // 浏览足迹-公告
      "2-1": 0, // 浏览足迹-职位
    }
    this.setData({
      baseInfo: APP.globalData.userInfo,
    })
    this.getHeight()
  },
  getConfig() {
    const data = APP.globalData?.serverConfig?.job_tag || []
    let tagOptionsData = []
    const follows_tag = this.data.currentJobInfo.follows_tag
    if (data.length) {
      tagOptionsData = data.map((item) => {
        if (follows_tag == item.key) {
          return {
            name: "取消" + item.name,
            value: item.key,
          }
        } else {
          return {
            name: "加入" + item.name,
            value: item.key,
          }
        }
      })
    }
    this.setData({
      tagOptionsData,
    })
  },
  /**
   * 获取列表数据 - 支持分页加载
   * @param {boolean} isLoadMore - 是否为加载更多（分页）
   */
  async getList(isLoadMore = false) {
    const currentTabKey = this.getCurrentTabKey()
    const currentCache = this.data.tabDataCache[currentTabKey]

    // 如果正在加载，直接返回
    if (this.data.isLoading) {
      return
    }

    // 如果是加载更多但没有更多数据，直接返回
    if (isLoadMore && !currentCache.hasMore) {
      return
    }

    try {
      // 设置加载状态
      this.setData({
        isLoading: true,
      })

      // 构建请求参数
      const requestParams = this.buildRequestParams(
        isLoadMore ? currentCache.page : 1
      )

      // 发起请求
      const res = await UTIL.request(requestParams.url, requestParams.params)

      if (res && res.error && res.error.code === 0) {
        this.handleListResponse(res, isLoadMore, currentTabKey)
      } else {
        this.handleRequestError()
      }
    } catch (error) {
      console.error("获取列表数据异常:", error)
      this.handleRequestError()
    } finally {
      this.setData({
        isLoading: false,
      })
    }
  },

  /**
   * 构建请求参数
   * @param {number} page - 页码
   */
  buildRequestParams(page) {
    const { activeTabIndex, subActiveTabIndex, size } = this.data

    // 确定API URL
    const url = activeTabIndex === 2 ? API.getFootPrintList : API.getFollowsList

    // 确定item_type
    let item_type
    if (activeTabIndex === 0) {
      item_type = "article"
    } else if (activeTabIndex === 1) {
      item_type = "job"
    } else {
      // activeTabIndex === 2 (浏览足迹)
      item_type = subActiveTabIndex === 0 ? "article" : "job"
    }

    return {
      url,
      params: {
        item_type,
        page,
        size,
      },
    }
  },

  /**
   * 处理列表响应数据
   * @param {Object} res - 响应数据
   * @param {boolean} isLoadMore - 是否为加载更多
   * @param {string} currentTabKey - 当前tab标识
   */
  handleListResponse(res, isLoadMore, currentTabKey) {
    const resData = res?.data
    let newList = []
    let tips = ""

    // 根据不同的tab处理数据结构
    if (this.data.activeTabIndex === 2) {
      // 浏览足迹返回的是 { list: [], tips: "" }
      newList = resData?.list || []
      tips = resData?.tips || ""
    } else {
      // 关注列表直接返回数组
      newList = resData || []
    }

    // 获取当前缓存
    const currentCache = this.data.tabDataCache[currentTabKey]

    // 更新数据
    let updatedList
    if (isLoadMore) {
      // 分页加载：追加到现有数据
      updatedList = [...currentCache.list, ...newList]
    } else {
      // 首次加载或刷新：直接使用新数据
      updatedList = newList
    }

    // 判断是否还有更多数据
    const hasMore = newList.length >= this.data.size

    // 更新缓存
    const updatedCache = {
      ...this.data.tabDataCache,
      [currentTabKey]: {
        list: updatedList,
        hasMore,
        page: isLoadMore ? currentCache.page + 1 : 2, // 下次请求的页码
      },
    }

    // 更新页面数据
    this.setData({
      dataList: updatedList,
      tips,
      hasMore,
      page: updatedCache[currentTabKey].page,
      tabDataCache: updatedCache,
    })
  },

  /**
   * 处理请求错误
   */
  handleRequestError() {
    const currentTabKey = this.getCurrentTabKey()

    this.setData({
      hasMore: false,
      [`tabDataCache.${currentTabKey}.hasMore`]: false,
    })

    wx.showToast({
      title: "加载失败，请重试",
      icon: "none",
    })
  },
  getHeight() {
    wx.createSelectorQuery()
      .select(".tab-area")
      .boundingClientRect((res) => {
        const tabHeight = res.top
        this.setData({
          tabHeight,
        })
      })
      .exec()
    let menuInfo = wx.getMenuButtonBoundingClientRect()
    this.setData({
      navigationBarHeight: menuInfo.top + 32,
    })
  },

  // 获取当前tab的标识
  getCurrentTabKey() {
    const { activeTabIndex, subActiveTabIndex } = this.data
    return MyHomeDataService.generateTabKey(activeTabIndex, subActiveTabIndex)
  },

  onPageScroll(e) {
    const scrollTop = e.scrollTop
    const currentTabKey = this.getCurrentTabKey()
    const tabHeight = this.data.tabHeight
    // 记录当前tab的滚动位置
    this.tabScrollPositions[currentTabKey] = scrollTop

    if (scrollTop > tabHeight && !this.data.isSticky) {
      this.setData({
        isSticky: true,
      })
    }
    if (scrollTop < tabHeight && this.data.isSticky) {
      this.setData({
        isSticky: false,
      })
    }
  },
  // 处理职位卡片的三个点点击事件
  onJobOptionsClick(e) {
    let optionsData = [
      { name: "添加标签", value: "addTag" },
      { name: "加入对比", value: "addToCompare" },
      { name: "取消关注", value: "unfollow" },
    ]
    const { jobId, job_name, follows_id, follows_tag } = e.detail
    if (follows_tag) {
      optionsData[0].name = "修改标签"
    }
    if (this.data.pkListIds.length && this.data.pkListIds.includes(jobId)) {
      optionsData = [
        { name: "添加标签", value: "addTag" },
        { name: "取消关注", value: "unfollow" },
      ]
    }
    this.setData({
      currentJobInfo: {
        jobId: jobId,
        job_name: job_name,
        follows_id: follows_id,
        follows_tag: follows_tag,
      },
      optionsData,
      showOptionsPopup: true,
    })
  },
  goResume() {
    ROUTER.navigateTo({
      path: "/pages/my/resume/index",
    })
  },
  changeIndex(e) {
    const { index } = e.currentTarget.dataset
    if (index == this.data.activeTabIndex) {
      return
    }
    this.setData({
      activeTabIndex: index,
    })
    this.loadTabData()
    // 恢复对应tab的滚动位置
    this.restoreScrollPosition()
  },
  changeSubIndex(e) {
    const { index } = e.currentTarget.dataset
    this.setData({
      subActiveTabIndex: index,
    })
    this.loadTabData()
    // 恢复对应tab的滚动位置
    this.restoreScrollPosition()
  },

  /**
   * 加载tab数据 - 优先使用缓存
   */
  loadTabData() {
    const currentTabKey = this.getCurrentTabKey()
    const currentCache = this.data.tabDataCache[currentTabKey]

    // 如果有缓存数据，直接使用
    if (currentCache.list.length > 0) {
      this.setData({
        dataList: currentCache.list,
        hasMore: currentCache.hasMore,
        page: currentCache.page,
        isLoading: false,
      })
    } else {
      // 没有缓存数据，重新请求
      this.getList(false)
    }
  },

  /**
   * 刷新当前tab数据 - 清除缓存并重新请求
   */
  refreshCurrentTab() {
    const currentTabKey = this.getCurrentTabKey()

    // 清除当前tab的缓存
    this.setData({
      [`tabDataCache.${currentTabKey}`]: {
        list: [],
        hasMore: true,
        page: 1,
      },
    })

    // 重新请求数据
    this.getList(false)
  },

  // 恢复滚动位置
  restoreScrollPosition() {
    const currentTabKey = this.getCurrentTabKey()
    const savedScrollTop = this.tabScrollPositions[currentTabKey] || 0
    console.log(this.tabScrollPositions, currentTabKey, savedScrollTop)

    wx.pageScrollTo({
      scrollTop: savedScrollTop,
      duration: 0, // 不使用动画，直接跳转
    })
  },

  closetip() {
    this.setData({
      tipsShow: false,
    })
  },

  // 关闭操作选项弹窗
  closeOptionsPopup() {
    this.setData({
      showOptionsPopup: false,
    })
  },

  // 关闭添加标签弹窗
  closeTagPopup() {
    this.setData({
      showTagPopup: false,
    })
  },

  // 处理一级弹窗选择事件
  onOptionsSelect(e) {
    const { value } = e.detail
    this.setData({
      showOptionsPopup: false,
    })

    switch (value) {
      case "addTag":
        this.getConfig()
        this.setData({
          showTagPopup: true,
        })
        break
      case "addToCompare":
        this.addToCompare()
        break
      case "unfollow":
        wx.showModal({
          title: "确认取消关注",
          content: `确定要取消关注"${this.data.currentJobInfo?.job_name}"吗？`,
          success: (res) => {
            if (res.confirm) {
              UTIL.request(API.setFollows, {
                item_type: "job",
                item_no: [this.data.currentJobInfo.jobId],
                type: "unfollow",
              }).then((res) => {
                if (res) {
                  wx.showToast({
                    title: "已取消关注",
                    icon: "none",
                  })
                  this.refreshCurrentTab()
                }
              })
            }
          },
        })
        break
    }
  },

  // 处理二级弹窗选择事件
  async onTagSelect(e) {
    const { value } = e.detail
    const param = {
      follows_id: this.data.currentJobInfo.follows_id,
      tag: value,
    }
    const res = await UTIL.request(API.setFollowTag, param)
    if (res) {
      wx.showToast({
        title:
          value == this.data.currentJobInfo.follows_tag
            ? "取消成功"
            : "加入成功",
        icon: "none",
      })
      this.setData({
        showTagPopup: false,
      })
      this.refreshCurrentTab()
    }

    let toastTitle = ""
    switch (value) {
      case "addToApplication":
        toastTitle = "已加入报考岗位"
        break
      case "addToIntention":
        toastTitle = "已加入意向岗位"
        break
    }

    if (toastTitle) {
      wx.showToast({
        title: toastTitle,
        icon: "none",
      })
    }
  },

  addToCompare() {
    const newPkListIds = this.data.pkListIds
    if (!newPkListIds.includes(this.data.currentJobInfo.jobId)) {
      newPkListIds.push(this.data.currentJobInfo.jobId)
    }
    wx.setStorageSync("pkListIds", newPkListIds)
    this.setData({
      pkListIds: newPkListIds,
    })
    wx.showToast({
      title: "已加入对比",
      icon: "none",
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getPkListIds()
    this.loadTabData()
  },

  getPkListIds() {
    const pkListIds = wx.getStorageSync("pkListIds") || []
    this.setData({
      pkListIds,
    })
  },
  goComparison() {
    ROUTER.navigateTo({
      path: "/pages/job/comparisonList/index",
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log("下拉刷新")
    // 刷新当前tab数据
    this.refreshCurrentTab()

    // 停止下拉刷新
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log("触底了")
    // 检查是否还有更多数据且当前不在加载中
    if (this.data.hasMore && !this.data.isLoading) {
      // 加载下一页数据
      this.getList(true)
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
})
